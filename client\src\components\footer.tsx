import { Link } from "wouter";

export function Footer() {
  return (
    <footer className="bg-slate-800 text-white py-12 mt-16">
      <div className="max-w-6xl mx-auto px-4 grid grid-cols-1 md:grid-cols-4 gap-8">
        {/* Brand Section */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">🗣</span>
            </div>
            <span className="text-xl font-bold">Speech AI</span>
          </div>
          <p className="text-slate-300 text-sm leading-relaxed">
            Free text to speech converter with AI-powered natural voices supporting 100+ languages.
          </p>
        </div>

        {/* Features Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Features</h3>
          <ul className="space-y-2 text-sm text-slate-300">
            <li>Multiple Languages</li>
            <li>Natural AI Voices</li>
            <li>Free Downloads</li>
            <li>No Registration</li>
          </ul>
        </div>

        {/* Support Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Support</h3>
          <ul className="space-y-2 text-sm text-slate-300">
            <li>
              <Link href="/help" className="hover:text-white transition-colors">
                Help Center
              </Link>
            </li>
            <li>
              <Link href="/api-docs" className="hover:text-white transition-colors">
                API Documentation
              </Link>
            </li>
            <li>
              <Link href="/contact" className="hover:text-white transition-colors">
                Contact Us
              </Link>
            </li>
            <li>
              <Link href="/report" className="hover:text-white transition-colors">
                Report Issues
              </Link>
            </li>
          </ul>
        </div>

        {/* Legal Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Legal</h3>
          <ul className="space-y-2 text-sm text-slate-300">
            <li>
              <Link href="/privacy" className="hover:text-white transition-colors">
                Privacy Policy
              </Link>
            </li>
            <li>
              <Link href="/terms" className="hover:text-white transition-colors">
                Terms of Service
              </Link>
            </li>
            <li>
              <Link href="/cookies" className="hover:text-white transition-colors">
                Cookie Policy
              </Link>
            </li>
            <li>
              <Link href="/gdpr" className="hover:text-white transition-colors">
                GDPR Compliance
              </Link>
            </li>
          </ul>
        </div>
      </div>

      {/* Copyright */}
      <div className="border-t border-slate-700 mt-8 pt-6">
        <div className="max-w-6xl mx-auto px-4 text-center text-sm text-slate-400">
          © 2024 Speech AI. All rights reserved. | Free Text to Speech Converter
        </div>
      </div>
    </footer>
  );
}