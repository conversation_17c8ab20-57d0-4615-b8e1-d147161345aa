import { apiRequest } from "./queryClient";

export interface TtsRequest {
  text: string;
  language: string;
  voice: string;
  speed: string;
  pitch: string;
}

export interface TtsResponse {
  success: boolean;
  audioUrl?: string;
  requestId?: number;
  error?: string;
}

export interface LanguagesResponse {
  success: boolean;
  languages?: Array<{
    code: string;
    name: string;
    flag: string;
  }>;
  error?: string;
}

export interface VoicesResponse {
  success: boolean;
  voices?: Array<{
    id: string;
    name: string;
    gender: 'male' | 'female';
  }>;
  error?: string;
}

export async function generateSpeech(request: TtsRequest): Promise<TtsResponse> {
  try {
    const response = await apiRequest("POST", "/api/tts/generate", request);
    const result = await response.json();
    
    // If server indicates to use client-side synthesis
    if (result.useClientSide && result.config) {
      const audioUrl = await generateClientSideSpeech(result.config);
      return {
        success: true,
        audioUrl,
        requestId: result.requestId
      };
    }
    
    return result;
  } catch (error) {
    console.error("TTS Error:", error);
    return {
      success: false,
      error: "Failed to generate speech"
    };
  }
}

// Generate speech using browser's SpeechSynthesis API
async function generateClientSideSpeech(config: any): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!('speechSynthesis' in window)) {
      reject(new Error("Speech synthesis not supported in this browser"));
      return;
    }

    // Store the speech config as a special URL that the audio player will recognize
    const speechConfig = {
      text: config.text,
      language: config.language,
      voice: config.voice,
      speed: config.speed,
      pitch: config.pitch
    };

    // Create a special data URL that contains the speech configuration
    const configJson = JSON.stringify(speechConfig);
    const speechUrl = `data:application/json;base64,${btoa(configJson)}`;
    
    resolve(speechUrl);
  });
}

export async function getLanguages(): Promise<LanguagesResponse> {
  const response = await apiRequest("GET", "/api/tts/languages");
  return await response.json();
}

export async function getVoices(language: string): Promise<VoicesResponse> {
  const response = await apiRequest("GET", `/api/tts/voices/${language}`);
  return await response.json();
}
