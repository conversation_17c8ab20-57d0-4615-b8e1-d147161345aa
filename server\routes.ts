import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertTtsRequestSchema, type TtsApiRequest, type TtsApiResponse } from "@shared/schema";
import { z } from "zod";
import { randomUUID } from "crypto";

// Helper function to generate SSML
function generateSSML(text: string, voiceName: string, speed: string, pitch: string): string {
  const speedValue = parseFloat(speed) || 1.0;
  const pitchValue = parseInt(pitch) || 0;
  
  const speedPercent = Math.round((speedValue - 1) * 100);
  const pitchHz = pitchValue > 0 ? `+${pitchValue}Hz` : pitchValue < 0 ? `${pitchValue}Hz` : '0Hz';
  
  return `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>
    <voice name='${voiceName}'>
      <prosody rate='${speedPercent >= 0 ? '+' : ''}${speedPercent}%' pitch='${pitchHz}'>
        ${text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')}
      </prosody>
    </voice>
  </speak>`;
}

// Helper function to map language codes to Microsoft Edge voice names
function getMicrosoftVoiceName(language: string, voiceId: string): string {
  const voiceMap: Record<string, Record<string, string>> = {
    "en-US": {
      "en-US-Wavenet-D": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)",
      "en-US-Wavenet-F": "Microsoft Server Speech Text to Speech Voice (en-US, JennyNeural)",
      "en-US-Wavenet-A": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)",
      "en-US-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)"
    },
    "en-GB": {
      "en-GB-Wavenet-A": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)",
      "en-GB-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (en-GB, RyanNeural)"
    },
    "es-ES": {
      "es-ES-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (es-ES, AlvaroNeural)",
      "es-ES-Wavenet-C": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)"
    },
    "fr-FR": {
      "fr-FR-Wavenet-A": "Microsoft Server Speech Text to Speech Voice (fr-FR, DeniseNeural)",
      "fr-FR-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (fr-FR, HenriNeural)"
    },
    "de-DE": {
      "de-DE-Wavenet-A": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)",
      "de-DE-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (de-DE, ConradNeural)"
    },
    "zh-CN": {
      "zh-CN-Wavenet-A": "Microsoft Server Speech Text to Speech Voice (zh-CN, XiaoxiaoNeural)",
      "zh-CN-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunxiNeural)"
    },
    "ja-JP": {
      "ja-JP-Wavenet-A": "Microsoft Server Speech Text to Speech Voice (ja-JP, NanamiNeural)",
      "ja-JP-Wavenet-B": "Microsoft Server Speech Text to Speech Voice (ja-JP, KeitaNeural)"
    }
  };
  
  return voiceMap[language]?.[voiceId] || "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)";
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Generate speech using text-to-speech.online API
  app.post("/api/tts/generate", async (req, res) => {
    try {
      const validatedData = insertTtsRequestSchema.parse(req.body);
      
      if (!validatedData.text || validatedData.text.length > 5000) {
        return res.status(400).json({ 
          success: false, 
          error: "Text is required and must be less than 5000 characters" 
        });
      }

      // Create TTS request record
      const ttsRequest = await storage.createTtsRequest(validatedData);

      // Try VoiceRSS API first for real audio file generation
      try {
        const voiceRSSKey = process.env.VOICERSS_API_KEY;
        console.log('VoiceRSS API Key available:', !!voiceRSSKey);
        
        if (voiceRSSKey) {
          const voiceRSSUrl = 'https://api.voicerss.org/';
          const voiceRSSParams = new URLSearchParams({
            key: voiceRSSKey,
            src: validatedData.text,
            hl: validatedData.language,
            r: validatedData.speed || "0",
            c: 'MP3',
            f: '44khz_16bit_stereo',
            ssml: 'false'
          });

          console.log('VoiceRSS request params:', {
            hl: validatedData.language,
            textLength: validatedData.text.length,
            speed: validatedData.speed
          });

          const voiceRSSResponse = await fetch(voiceRSSUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: voiceRSSParams.toString()
          });

          console.log('VoiceRSS response status:', voiceRSSResponse.status);
          console.log('VoiceRSS response content-type:', voiceRSSResponse.headers.get('content-type'));

          if (voiceRSSResponse.ok) {
            const contentType = voiceRSSResponse.headers.get('content-type');
            if (contentType?.includes('audio')) {
              const audioBuffer = await voiceRSSResponse.arrayBuffer();
              const audioUrl = `data:audio/mp3;base64,${Buffer.from(audioBuffer).toString('base64')}`;
              
              await storage.updateTtsRequestAudio(ttsRequest.id, audioUrl);
              
              console.log('VoiceRSS API success, returning audio URL');
              return res.json({ 
                success: true, 
                audioUrl: audioUrl,
                requestId: ttsRequest.id 
              });
            } else {
              const errorText = await voiceRSSResponse.text();
              console.log('VoiceRSS API error response:', errorText);
            }
          } else {
            const errorText = await voiceRSSResponse.text();
            console.log('VoiceRSS API HTTP error:', voiceRSSResponse.status, errorText);
          }
        } else {
          console.log('No VoiceRSS API key found');
        }
      } catch (voiceRSSError) {
        console.log('VoiceRSS API failed, falling back to client-side synthesis:', voiceRSSError);
      }

      // Fallback to client-side synthesis for playback only
      const ttsConfig = {
        text: validatedData.text,
        language: validatedData.language,
        voice: validatedData.voice,
        speed: parseFloat(validatedData.speed || "1.0"),
        pitch: parseFloat(validatedData.pitch || "0")
      };

      await storage.updateTtsRequestAudio(ttsRequest.id, "client-side-synthesis");
      
      res.json({ 
        success: true, 
        useClientSide: true,
        config: ttsConfig,
        requestId: ttsRequest.id 
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ 
          success: false, 
          error: "Invalid request data", 
          details: error.errors 
        });
      } else {
        console.error("TTS Generation Error:", error);
        res.status(500).json({ 
          success: false, 
          error: "Internal server error" 
        });
      }
    }
  });

  // Get available languages and voices
  app.get("/api/tts/languages", async (req, res) => {
    try {
      // Import from shared language configuration
      const { defaultLanguages } = await import("../client/src/lib/languages.ts");
      
      res.json({ success: true, languages: defaultLanguages });
    } catch (error) {
      console.error("Languages API Error:", error);
      res.status(500).json({ 
        success: false, 
        error: "Failed to fetch languages" 
      });
    }
  });

  // Get available voices for a language
  app.get("/api/tts/voices/:language", async (req, res) => {
    try {
      const { language } = req.params;
      
      // Import from shared voice configuration
      const { defaultVoices } = await import("../client/src/lib/languages.ts");

      const voices = defaultVoices[language] || defaultVoices["en-US"] || [];
      res.json({ success: true, voices });
    } catch (error) {
      console.error("Voices API Error:", error);
      res.status(500).json({ 
        success: false, 
        error: "Failed to fetch voices" 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
