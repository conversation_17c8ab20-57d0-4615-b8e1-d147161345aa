export interface Language {
  code: string;
  name: string;
  flag: string;
}

export interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
}

export const defaultLanguages: Language[] = [
  // English variants (comprehensive list matching text-to-speech.online)
  { code: "en-AU", name: "English (Australia)", flag: "🇦🇺" },
  { code: "en-CA", name: "English (Canada)", flag: "🇨🇦" },
  { code: "en-HK", name: "English (Hong Kong)", flag: "🇭🇰" },
  { code: "en-IN", name: "English (India)", flag: "🇮🇳" },
  { code: "en-IE", name: "English (Ireland)", flag: "🇮🇪" },
  { code: "en-KE", name: "English (Kenya)", flag: "🇰🇪" },
  { code: "en-NZ", name: "English (New Zealand)", flag: "🇳🇿" },
  { code: "en-NG", name: "English (Nigeria)", flag: "🇳🇬" },
  { code: "en-PH", name: "English (Philippines)", flag: "🇵🇭" },
  { code: "en-SG", name: "English (Singapore)", flag: "🇸🇬" },
  { code: "en-ZA", name: "English (South Africa)", flag: "🇿🇦" },
  { code: "en-TZ", name: "English (Tanzania)", flag: "🇹🇿" },
  { code: "en-GB", name: "English (United Kingdom)", flag: "🇬🇧" },
  { code: "en-US", name: "English (United States)", flag: "🇺🇸" },
  
  // Spanish variants (comprehensive list)
  { code: "es-CO", name: "Spanish (Colombia)", flag: "🇨🇴" },
  { code: "es-ES", name: "Spanish (Spain)", flag: "🇪🇸" },
  { code: "es-CR", name: "Spanish (Costa Rica)", flag: "🇨🇷" },
  { code: "es-CU", name: "Spanish (Cuba)", flag: "🇨🇺" },
  { code: "es-DO", name: "Spanish (Dominican Republic)", flag: "🇩🇴" },
  { code: "es-EC", name: "Spanish (Ecuador)", flag: "🇪🇨" },
  { code: "es-SV", name: "Spanish (El Salvador)", flag: "🇸🇻" },
  { code: "es-GQ", name: "Spanish (Equatorial Guinea)", flag: "🇬🇶" },
  { code: "es-GT", name: "Spanish (Guatemala)", flag: "🇬🇹" },
  { code: "es-HN", name: "Spanish (Honduras)", flag: "🇭🇳" },
  { code: "es-MX", name: "Spanish (Mexico)", flag: "🇲🇽" },
  { code: "es-NI", name: "Spanish (Nicaragua)", flag: "🇳🇮" },
  { code: "es-PA", name: "Spanish (Panama)", flag: "🇵🇦" },
  { code: "es-PY", name: "Spanish (Paraguay)", flag: "🇵🇾" },
  { code: "es-PE", name: "Spanish (Peru)", flag: "🇵🇪" },
  { code: "es-PR", name: "Spanish (Puerto Rico)", flag: "🇵🇷" },
  { code: "es-US", name: "Spanish (United States)", flag: "🇺🇸" },
  { code: "es-UY", name: "Spanish (Uruguay)", flag: "🇺🇾" },
  { code: "es-VE", name: "Spanish (Venezuela)", flag: "🇻🇪" },
  { code: "es-AR", name: "Spanish (Argentina)", flag: "🇦🇷" },
  { code: "fr-FR", name: "French (France)", flag: "🇫🇷" },
  { code: "fr-CA", name: "French (Canada)", flag: "🇨🇦" },
  { code: "de-DE", name: "German (Germany)", flag: "🇩🇪" },
  { code: "de-AT", name: "German (Austria)", flag: "🇦🇹" },
  { code: "it-IT", name: "Italian (Italy)", flag: "🇮🇹" },
  { code: "pt-BR", name: "Portuguese (Brazil)", flag: "🇧🇷" },
  { code: "pt-PT", name: "Portuguese (Portugal)", flag: "🇵🇹" },
  { code: "ru-RU", name: "Russian (Russia)", flag: "🇷🇺" },
  { code: "ja-JP", name: "Japanese (Japan)", flag: "🇯🇵" },
  { code: "ko-KR", name: "Korean (South Korea)", flag: "🇰🇷" },
  // Chinese variants
  { code: "zh-CN", name: "Chinese (Mainland)", flag: "🇨🇳" },
  { code: "zh-TW", name: "Chinese (Taiwan)", flag: "🇹🇼" },
  { code: "zh-HK", name: "Chinese (Hong Kong)", flag: "🇭🇰" },
  
  // Arabic variants (comprehensive list)
  { code: "ar-ET", name: "Amharic (Ethiopia)", flag: "🇪🇹" },
  { code: "ar-DZ", name: "Arabic (Algeria)", flag: "🇩🇿" },
  { code: "ar-BH", name: "Arabic (Bahrain)", flag: "🇧🇭" },
  { code: "ar-EG", name: "Arabic (Egypt)", flag: "🇪🇬" },
  { code: "ar-IQ", name: "Arabic (Iraq)", flag: "🇮🇶" },
  { code: "ar-JO", name: "Arabic (Jordan)", flag: "🇯🇴" },
  { code: "ar-KW", name: "Arabic (Kuwait)", flag: "🇰🇼" },
  { code: "ar-LB", name: "Arabic (Lebanon)", flag: "🇱🇧" },
  { code: "ar-LY", name: "Arabic (Libya)", flag: "🇱🇾" },
  { code: "ar-MA", name: "Arabic (Morocco)", flag: "🇲🇦" },
  { code: "ar-OM", name: "Arabic (Oman)", flag: "🇴🇲" },
  { code: "ar-QA", name: "Arabic (Qatar)", flag: "🇶🇦" },
  { code: "ar-SA", name: "Arabic (Saudi Arabia)", flag: "🇸🇦" },
  { code: "ar-SY", name: "Arabic (Syria)", flag: "🇸🇾" },
  { code: "ar-TN", name: "Arabic (Tunisia)", flag: "🇹🇳" },
  { code: "ar-AE", name: "Arabic (United Arab Emirates)", flag: "🇦🇪" },
  { code: "ar-YE", name: "Arabic (Yemen)", flag: "🇾🇪" },
  { code: "az-AZ", name: "Azerbaijan (Azerbaijan)", flag: "🇦🇿" },
  { code: "hi-IN", name: "Hindi (India)", flag: "🇮🇳" },
  { code: "th-TH", name: "Thai (Thailand)", flag: "🇹🇭" },
  { code: "vi-VN", name: "Vietnamese (Vietnam)", flag: "🇻🇳" },
  { code: "tr-TR", name: "Turkish (Turkey)", flag: "🇹🇷" },
  { code: "nl-NL", name: "Dutch (Netherlands)", flag: "🇳🇱" },
  { code: "sv-SE", name: "Swedish (Sweden)", flag: "🇸🇪" },
  { code: "da-DK", name: "Danish (Denmark)", flag: "🇩🇰" },
  { code: "no-NO", name: "Norwegian (Norway)", flag: "🇳🇴" },
  { code: "fi-FI", name: "Finnish (Finland)", flag: "🇫🇮" },
  { code: "pl-PL", name: "Polish (Poland)", flag: "🇵🇱" }
];

export const defaultVoices: Record<string, Voice[]> = {
  // English variants - all use similar voice options but with regional IDs
  "en-AU": [
    { id: "en-AU-Wavenet-A", name: "Nicole", gender: "female" },
    { id: "en-AU-Wavenet-B", name: "Russell", gender: "male" }
  ],
  "en-CA": [
    { id: "en-CA-Wavenet-A", name: "Clara", gender: "female" },
    { id: "en-CA-Wavenet-B", name: "Liam", gender: "male" }
  ],
  "en-HK": [
    { id: "en-HK-Wavenet-A", name: "Tracy", gender: "female" },
    { id: "en-HK-Wavenet-B", name: "Danny", gender: "male" }
  ],
  "en-IN": [
    { id: "en-IN-Wavenet-A", name: "Aditi", gender: "female" },
    { id: "en-IN-Wavenet-B", name: "Ravi", gender: "male" }
  ],
  "en-IE": [
    { id: "en-IE-Wavenet-A", name: "Emily", gender: "female" },
    { id: "en-IE-Wavenet-B", name: "Brian", gender: "male" }
  ],
  "en-KE": [
    { id: "en-KE-Wavenet-A", name: "Grace", gender: "female" },
    { id: "en-KE-Wavenet-B", name: "Samuel", gender: "male" }
  ],
  "en-NZ": [
    { id: "en-NZ-Wavenet-A", name: "Aria", gender: "female" },
    { id: "en-NZ-Wavenet-B", name: "Guy", gender: "male" }
  ],
  "en-NG": [
    { id: "en-NG-Wavenet-A", name: "Ezinne", gender: "female" },
    { id: "en-NG-Wavenet-B", name: "Abayomi", gender: "male" }
  ],
  "en-PH": [
    { id: "en-PH-Wavenet-A", name: "Rosa", gender: "female" },
    { id: "en-PH-Wavenet-B", name: "Joey", gender: "male" }
  ],
  "en-SG": [
    { id: "en-SG-Wavenet-A", name: "Luna", gender: "female" },
    { id: "en-SG-Wavenet-B", name: "Wayne", gender: "male" }
  ],
  "en-ZA": [
    { id: "en-ZA-Wavenet-A", name: "Ayanda", gender: "female" },
    { id: "en-ZA-Wavenet-B", name: "Willem", gender: "male" }
  ],
  "en-TZ": [
    { id: "en-TZ-Wavenet-A", name: "Zara", gender: "female" },
    { id: "en-TZ-Wavenet-B", name: "Juma", gender: "male" }
  ],
  "en-GB": [
    { id: "en-GB-Wavenet-A", name: "Alice", gender: "female" },
    { id: "en-GB-Wavenet-B", name: "Arthur", gender: "male" }
  ],
  "en-US": [
    { id: "en-US-Wavenet-D", name: "David", gender: "male" },
    { id: "en-US-Wavenet-F", name: "Sarah", gender: "female" },
    { id: "en-US-Wavenet-A", name: "Emma", gender: "female" },
    { id: "en-US-Wavenet-B", name: "Mike", gender: "male" }
  ],
  
  // Spanish variants
  "es-CO": [
    { id: "es-CO-Wavenet-A", name: "Camila", gender: "female" },
    { id: "es-CO-Wavenet-B", name: "Andres", gender: "male" }
  ],
  "es-ES": [
    { id: "es-ES-Wavenet-B", name: "Carlos", gender: "male" },
    { id: "es-ES-Wavenet-C", name: "Maria", gender: "female" }
  ],
  "es-CR": [
    { id: "es-CR-Wavenet-A", name: "Sofia", gender: "female" },
    { id: "es-CR-Wavenet-B", name: "Jose", gender: "male" }
  ],
  "es-CU": [
    { id: "es-CU-Wavenet-A", name: "Isabel", gender: "female" },
    { id: "es-CU-Wavenet-B", name: "Roberto", gender: "male" }
  ],
  "es-DO": [
    { id: "es-DO-Wavenet-A", name: "Carmen", gender: "female" },
    { id: "es-DO-Wavenet-B", name: "Miguel", gender: "male" }
  ],
  "es-EC": [
    { id: "es-EC-Wavenet-A", name: "Andrea", gender: "female" },
    { id: "es-EC-Wavenet-B", name: "Fernando", gender: "male" }
  ],
  "es-SV": [
    { id: "es-SV-Wavenet-A", name: "Gabriela", gender: "female" },
    { id: "es-SV-Wavenet-B", name: "Salvador", gender: "male" }
  ],
  "es-GQ": [
    { id: "es-GQ-Wavenet-A", name: "Victoria", gender: "female" },
    { id: "es-GQ-Wavenet-B", name: "Manuel", gender: "male" }
  ],
  "es-GT": [
    { id: "es-GT-Wavenet-A", name: "Lucia", gender: "female" },
    { id: "es-GT-Wavenet-B", name: "Carlos", gender: "male" }
  ],
  "es-HN": [
    { id: "es-HN-Wavenet-A", name: "Patricia", gender: "female" },
    { id: "es-HN-Wavenet-B", name: "Hector", gender: "male" }
  ],
  "es-MX": [
    { id: "es-MX-Wavenet-A", name: "Mia", gender: "female" },
    { id: "es-MX-Wavenet-B", name: "Diego", gender: "male" }
  ],
  "es-NI": [
    { id: "es-NI-Wavenet-A", name: "Elena", gender: "female" },
    { id: "es-NI-Wavenet-B", name: "Nicolas", gender: "male" }
  ],
  "es-PA": [
    { id: "es-PA-Wavenet-A", name: "Paola", gender: "female" },
    { id: "es-PA-Wavenet-B", name: "Pablo", gender: "male" }
  ],
  "es-PY": [
    { id: "es-PY-Wavenet-A", name: "Rosa", gender: "female" },
    { id: "es-PY-Wavenet-B", name: "Pedro", gender: "male" }
  ],
  "es-PE": [
    { id: "es-PE-Wavenet-A", name: "Valentina", gender: "female" },
    { id: "es-PE-Wavenet-B", name: "Antonio", gender: "male" }
  ],
  "es-PR": [
    { id: "es-PR-Wavenet-A", name: "Bianca", gender: "female" },
    { id: "es-PR-Wavenet-B", name: "Ricardo", gender: "male" }
  ],
  "es-US": [
    { id: "es-US-Wavenet-A", name: "Penelope", gender: "female" },
    { id: "es-US-Wavenet-B", name: "Miguel", gender: "male" }
  ],
  "es-UY": [
    { id: "es-UY-Wavenet-A", name: "Valentina", gender: "female" },
    { id: "es-UY-Wavenet-B", name: "Mateo", gender: "male" }
  ],
  "es-VE": [
    { id: "es-VE-Wavenet-A", name: "Mariana", gender: "female" },
    { id: "es-VE-Wavenet-B", name: "Sebastian", gender: "male" }
  ],
  "es-AR": [
    { id: "es-AR-Wavenet-A", name: "Lupe", gender: "female" },
    { id: "es-AR-Wavenet-B", name: "Tomas", gender: "male" }
  ],
  "fr-FR": [
    { id: "fr-FR-Wavenet-A", name: "Marie", gender: "female" },
    { id: "fr-FR-Wavenet-B", name: "Pierre", gender: "male" }
  ],
  "fr-CA": [
    { id: "fr-CA-Wavenet-A", name: "Gabrielle", gender: "female" },
    { id: "fr-CA-Wavenet-B", name: "Jean", gender: "male" }
  ],
  "de-DE": [
    { id: "de-DE-Wavenet-A", name: "Marlene", gender: "female" },
    { id: "de-DE-Wavenet-B", name: "Hans", gender: "male" }
  ],
  "de-AT": [
    { id: "de-AT-Wavenet-A", name: "Vicki", gender: "female" },
    { id: "de-AT-Wavenet-B", name: "Michael", gender: "male" }
  ],
  "it-IT": [
    { id: "it-IT-Wavenet-A", name: "Carla", gender: "female" },
    { id: "it-IT-Wavenet-B", name: "Giorgio", gender: "male" }
  ],
  "pt-BR": [
    { id: "pt-BR-Wavenet-A", name: "Vitoria", gender: "female" },
    { id: "pt-BR-Wavenet-B", name: "Ricardo", gender: "male" }
  ],
  "pt-PT": [
    { id: "pt-PT-Wavenet-A", name: "Ines", gender: "female" },
    { id: "pt-PT-Wavenet-B", name: "Cristiano", gender: "male" }
  ],
  "zh-CN": [
    { id: "zh-CN-Wavenet-A", name: "Xiaoxiao", gender: "female" },
    { id: "zh-CN-Wavenet-B", name: "Yunyang", gender: "male" }
  ],
  "zh-TW": [
    { id: "zh-TW-Wavenet-A", name: "Zhiyu", gender: "female" },
    { id: "zh-TW-Wavenet-B", name: "Yunjian", gender: "male" }
  ],
  "zh-HK": [
    { id: "zh-HK-Wavenet-A", name: "Tracy", gender: "female" },
    { id: "zh-HK-Wavenet-B", name: "Danny", gender: "male" }
  ],
  
  // Arabic variants
  "ar-ET": [
    { id: "ar-ET-Wavenet-A", name: "Meron", gender: "female" },
    { id: "ar-ET-Wavenet-B", name: "Dawit", gender: "male" }
  ],
  "ar-DZ": [
    { id: "ar-DZ-Wavenet-A", name: "Amina", gender: "female" },
    { id: "ar-DZ-Wavenet-B", name: "Omar", gender: "male" }
  ],
  "ar-BH": [
    { id: "ar-BH-Wavenet-A", name: "Layla", gender: "female" },
    { id: "ar-BH-Wavenet-B", name: "Khalid", gender: "male" }
  ],
  "ar-EG": [
    { id: "ar-EG-Wavenet-A", name: "Nour", gender: "female" },
    { id: "ar-EG-Wavenet-B", name: "Ahmed", gender: "male" }
  ],
  "ar-IQ": [
    { id: "ar-IQ-Wavenet-A", name: "Zahra", gender: "female" },
    { id: "ar-IQ-Wavenet-B", name: "Ali", gender: "male" }
  ],
  "ar-JO": [
    { id: "ar-JO-Wavenet-A", name: "Rania", gender: "female" },
    { id: "ar-JO-Wavenet-B", name: "Mohammad", gender: "male" }
  ],
  "ar-KW": [
    { id: "ar-KW-Wavenet-A", name: "Fatima", gender: "female" },
    { id: "ar-KW-Wavenet-B", name: "Yousef", gender: "male" }
  ],
  "ar-LB": [
    { id: "ar-LB-Wavenet-A", name: "Maya", gender: "female" },
    { id: "ar-LB-Wavenet-B", name: "Samir", gender: "male" }
  ],
  "ar-LY": [
    { id: "ar-LY-Wavenet-A", name: "Aisha", gender: "female" },
    { id: "ar-LY-Wavenet-B", name: "Muammar", gender: "male" }
  ],
  "ar-MA": [
    { id: "ar-MA-Wavenet-A", name: "Aicha", gender: "female" },
    { id: "ar-MA-Wavenet-B", name: "Youssef", gender: "male" }
  ],
  "ar-OM": [
    { id: "ar-OM-Wavenet-A", name: "Salma", gender: "female" },
    { id: "ar-OM-Wavenet-B", name: "Sultan", gender: "male" }
  ],
  "ar-QA": [
    { id: "ar-QA-Wavenet-A", name: "Maryam", gender: "female" },
    { id: "ar-QA-Wavenet-B", name: "Hamad", gender: "male" }
  ],
  "ar-SA": [
    { id: "ar-SA-Wavenet-A", name: "Hala", gender: "female" },
    { id: "ar-SA-Wavenet-B", name: "Naayf", gender: "male" }
  ],
  "ar-SY": [
    { id: "ar-SY-Wavenet-A", name: "Lina", gender: "female" },
    { id: "ar-SY-Wavenet-B", name: "Bashar", gender: "male" }
  ],
  "ar-TN": [
    { id: "ar-TN-Wavenet-A", name: "Leila", gender: "female" },
    { id: "ar-TN-Wavenet-B", name: "Habib", gender: "male" }
  ],
  "ar-AE": [
    { id: "ar-AE-Wavenet-A", name: "Amira", gender: "female" },
    { id: "ar-AE-Wavenet-B", name: "Rashid", gender: "male" }
  ],
  "ar-YE": [
    { id: "ar-YE-Wavenet-A", name: "Arwa", gender: "female" },
    { id: "ar-YE-Wavenet-B", name: "Abdulmalik", gender: "male" }
  ],
  "az-AZ": [
    { id: "az-AZ-Wavenet-A", name: "Gunel", gender: "female" },
    { id: "az-AZ-Wavenet-B", name: "Babek", gender: "male" }
  ]
};
