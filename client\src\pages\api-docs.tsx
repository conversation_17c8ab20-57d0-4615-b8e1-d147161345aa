import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft } from "lucide-react";
import { <PERSON> } from "wouter";

export default function ApiDocs() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
        
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">API Documentation</h1>
          <p className="text-lg text-gray-600">Integrate Speech AI into your applications</p>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Speech AI provides a RESTful API for text-to-speech conversion with support for multiple languages and voices.
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Base URL:</strong> <code className="bg-white px-2 py-1 rounded">https://your-domain.com/api</code>
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Endpoints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Generate Speech */}
              <div className="border-l-4 border-blue-500 pl-4">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="default">POST</Badge>
                  <code className="text-sm">/tts/generate</code>
                </div>
                <p className="text-gray-600 mb-3">Generate speech from text</p>
                
                <h4 className="font-semibold mb-2">Request Body:</h4>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`{
  "text": "Hello, world!",
  "language": "en-US",
  "voice": "male",
  "speed": "1.0",
  "pitch": "0"
}`}
                </pre>
                
                <h4 className="font-semibold mb-2 mt-4">Response:</h4>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`{
  "success": true,
  "audioUrl": "data:audio/mp3;base64,//audio_data",
  "requestId": 123
}`}
                </pre>
              </div>

              {/* Get Languages */}
              <div className="border-l-4 border-green-500 pl-4">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secondary">GET</Badge>
                  <code className="text-sm">/tts/languages</code>
                </div>
                <p className="text-gray-600 mb-3">Get list of supported languages</p>
                
                <h4 className="font-semibold mb-2">Response:</h4>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`{
  "success": true,
  "languages": [
    {
      "code": "en-US",
      "name": "English (United States)",
      "flag": "🇺🇸"
    },
    {
      "code": "es-ES",
      "name": "Spanish (Spain)",
      "flag": "🇪🇸"
    }
  ]
}`}
                </pre>
              </div>

              {/* Get Voices */}
              <div className="border-l-4 border-purple-500 pl-4">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">GET</Badge>
                  <code className="text-sm">/tts/voices?language=en-US</code>
                </div>
                <p className="text-gray-600 mb-3">Get available voices for a language</p>
                
                <h4 className="font-semibold mb-2">Response:</h4>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`{
  "success": true,
  "voices": [
    {
      "id": "male",
      "name": "Male Voice",
      "gender": "male"
    },
    {
      "id": "female",
      "name": "Female Voice",
      "gender": "female"
    }
  ]
}`}
                </pre>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Parameters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">Parameter</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Type</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Required</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>text</code></td>
                      <td className="border border-gray-300 px-4 py-2">string</td>
                      <td className="border border-gray-300 px-4 py-2">Yes</td>
                      <td className="border border-gray-300 px-4 py-2">Text to convert to speech</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>language</code></td>
                      <td className="border border-gray-300 px-4 py-2">string</td>
                      <td className="border border-gray-300 px-4 py-2">Yes</td>
                      <td className="border border-gray-300 px-4 py-2">Language code (e.g., en-US, es-ES)</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>voice</code></td>
                      <td className="border border-gray-300 px-4 py-2">string</td>
                      <td className="border border-gray-300 px-4 py-2">Yes</td>
                      <td className="border border-gray-300 px-4 py-2">Voice identifier (male, female, etc.)</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>speed</code></td>
                      <td className="border border-gray-300 px-4 py-2">string</td>
                      <td className="border border-gray-300 px-4 py-2">No</td>
                      <td className="border border-gray-300 px-4 py-2">Speech speed (0.5 to 2.0, default: 1.0)</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>pitch</code></td>
                      <td className="border border-gray-300 px-4 py-2">string</td>
                      <td className="border border-gray-300 px-4 py-2">No</td>
                      <td className="border border-gray-300 px-4 py-2">Voice pitch (-20 to 20, default: 0)</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Example Implementation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <h4 className="font-semibold">JavaScript/Node.js</h4>
              <pre className="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`const response = await fetch('/api/tts/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: 'Hello, world!',
    language: 'en-US',
    voice: 'male',
    speed: '1.0',
    pitch: '0'
  })
});

const data = await response.json();
if (data.success) {
  // Use data.audioUrl for playback or download
  console.log('Audio generated:', data.audioUrl);
}`}
              </pre>

              <h4 className="font-semibold">Python</h4>
              <pre className="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`import requests

url = "http://your-domain.com/api/tts/generate"
payload = {
    "text": "Hello, world!",
    "language": "en-US", 
    "voice": "male",
    "speed": "1.0",
    "pitch": "0"
}

response = requests.post(url, json=payload)
data = response.json()

if data["success"]:
    audio_url = data["audioUrl"]
    print(f"Audio generated: {audio_url}")`}
              </pre>

              <h4 className="font-semibold">cURL</h4>
              <pre className="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`curl -X POST http://your-domain.com/api/tts/generate \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "Hello, world!",
    "language": "en-US",
    "voice": "male", 
    "speed": "1.0",
    "pitch": "0"
  }'`}
              </pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Error Handling</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                The API returns standard HTTP status codes and error messages in JSON format.
              </p>
              
              <h4 className="font-semibold">Error Response Format:</h4>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`{
  "success": false,
  "error": "Error description"
}`}
              </pre>

              <h4 className="font-semibold">Common Error Codes:</h4>
              <ul className="space-y-2 text-sm">
                <li><strong>400:</strong> Bad Request - Invalid parameters</li>
                <li><strong>404:</strong> Not Found - Endpoint not found</li>
                <li><strong>429:</strong> Too Many Requests - Rate limit exceeded</li>
                <li><strong>500:</strong> Internal Server Error - Server error</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rate Limits</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                To ensure fair usage and system stability, the following rate limits apply:
              </p>
              <ul className="space-y-2">
                <li>• <strong>Free users:</strong> 100 requests per hour</li>
                <li>• <strong>Text length:</strong> Maximum 5000 characters per request</li>
                <li>• <strong>Concurrent requests:</strong> Maximum 5 simultaneous requests</li>
              </ul>
              <div className="bg-amber-50 p-4 rounded-lg mt-4">
                <p className="text-sm text-amber-800">
                  Rate limit headers are included in API responses to help you track usage.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}