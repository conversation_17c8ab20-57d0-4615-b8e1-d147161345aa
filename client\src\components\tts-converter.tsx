import { useState, useEffect } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Play, Eraser, Gauge, Music } from "lucide-react";
import { LanguageSelector } from "./language-selector";
import { AudioPlayer } from "./audio-player";
import { generateSpeech, getLanguages, getVoices } from "@/lib/tts-service";
import { defaultLanguages, defaultVoices } from "@/lib/languages";
import { useToast } from "@/hooks/use-toast";

export function TtsConverter() {
  const [text, setText] = useState("Welcome to our free text to speech converter. This tool allows you to convert any text into natural-sounding speech using advanced AI technology.");
  const [selectedLanguage, setSelectedLanguage] = useState("en-US");
  const [selectedVoice, setSelectedVoice] = useState("en-US-Wavenet-D");
  const [speed, setSpeed] = useState([1.0]);
  const [pitch, setPitch] = useState([0]);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  
  const { toast } = useToast();

  // Fetch languages
  const { data: languagesData, isLoading: languagesLoading } = useQuery({
    queryKey: ["/api/tts/languages"],
    select: (data) => data.languages || defaultLanguages,
  });

  // Fetch voices for selected language
  const { data: voicesData, isLoading: voicesLoading } = useQuery({
    queryKey: ["/api/tts/voices", selectedLanguage],
    select: (data) => data.voices || defaultVoices[selectedLanguage] || defaultVoices["en-US"],
  });

  // Update selected voice when language changes
  useEffect(() => {
    if (voicesData && voicesData.length > 0) {
      setSelectedVoice(voicesData[0].id);
    }
  }, [voicesData, selectedLanguage]);

  // Generate speech mutation
  const generateMutation = useMutation({
    mutationFn: generateSpeech,
    onSuccess: (data) => {
      if (data.success && data.audioUrl) {
        setAudioUrl(data.audioUrl);
        toast({
          title: "Success!",
          description: "Speech generated successfully",
        });
      } else {
        throw new Error(data.error || "Failed to generate speech");
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to generate speech",
        variant: "destructive",
      });
    },
  });

  const handleGenerate = () => {
    if (!text.trim()) {
      toast({
        title: "Error",
        description: "Please enter some text to convert",
        variant: "destructive",
      });
      return;
    }

    if (text.length > 5000) {
      toast({
        title: "Error",
        description: "Text is too long. Maximum 5000 characters allowed.",
        variant: "destructive",
      });
      return;
    }

    generateMutation.mutate({
      text: text.trim(),
      language: selectedLanguage,
      voice: selectedVoice,
      speed: speed[0].toString(),
      pitch: pitch[0].toString(),
    });
  };

  const handleClear = () => {
    setText("");
    setAudioUrl(null);
  };

  const getSpeedLabel = (value: number) => {
    if (value < 1) return `Slow (${value}x)`;
    if (value > 1) return `Fast (${value}x)`;
    return `Normal (${value}x)`;
  };

  const getPitchLabel = (value: number) => {
    if (value < 0) return `Low (${value > 0 ? '+' : ''}${value})`;
    if (value > 0) return `High (+${value})`;
    return `Normal (${value})`;
  };

  return (
    <Card className="bg-white shadow-lg border border-slate-200">
      <CardContent className="p-8">
        {/* Language and Voice Selection */}
        <div className="mb-8">
          <LanguageSelector
            languages={languagesData || defaultLanguages}
            voices={voicesData || defaultVoices[selectedLanguage] || defaultVoices["en-US"]}
            selectedLanguage={selectedLanguage}
            selectedVoice={selectedVoice}
            onLanguageChange={setSelectedLanguage}
            onVoiceChange={setSelectedVoice}
            isLoading={languagesLoading || voicesLoading}
          />
        </div>

        {/* Text Input */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <Label className="flex items-center text-sm font-medium text-slate-700">
              <Eraser className="w-4 h-4 mr-2" />
              Enter your text
            </Label>
            <span className="text-sm text-slate-500">
              {text.length} / 5000 characters
            </span>
          </div>
          <Textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Type or paste your text here. You can enter up to 5000 characters..."
            className="min-h-[160px] resize-none"
            maxLength={5000}
          />
        </div>

        {/* Voice Settings */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div>
            <Label className="flex items-center text-sm font-medium text-slate-700 mb-2">
              <Gauge className="w-4 h-4 mr-2" />
              Speech Speed
            </Label>
            <div className="space-y-2">
              <Slider
                value={speed}
                onValueChange={setSpeed}
                min={0.5}
                max={2}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-slate-500">
                <span>0.5x</span>
                <span>2.0x</span>
              </div>
              <div className="text-center">
                <span className="text-sm font-medium text-slate-700">
                  {getSpeedLabel(speed[0])}
                </span>
              </div>
            </div>
          </div>

          <div>
            <Label className="flex items-center text-sm font-medium text-slate-700 mb-2">
              <Music className="w-4 h-4 mr-2" />
              Voice Pitch
            </Label>
            <div className="space-y-2">
              <Slider
                value={pitch}
                onValueChange={setPitch}
                min={-20}
                max={20}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-slate-500">
                <span>Low</span>
                <span>High</span>
              </div>
              <div className="text-center">
                <span className="text-sm font-medium text-slate-700">
                  {getPitchLabel(pitch[0])}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mb-8">
          <Button
            onClick={handleGenerate}
            disabled={generateMutation.isPending || !text.trim()}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium"
          >
            {generateMutation.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Generate Speech
              </>
            )}
          </Button>

          <Button
            onClick={handleClear}
            variant="secondary"
            className="bg-slate-200 hover:bg-slate-300 text-slate-700 font-medium"
          >
            <Eraser className="w-4 h-4 mr-2" />
            Clear Text
          </Button>
        </div>

        {/* Loading State */}
        {generateMutation.isPending && (
          <Alert className="mb-8 bg-blue-50 border-blue-200">
            <Loader2 className="w-4 h-4 animate-spin" />
            <AlertDescription className="text-blue-700 font-medium">
              Generating speech... Please wait
            </AlertDescription>
          </Alert>
        )}

        {/* Error State */}
        {generateMutation.isError && (
          <Alert className="mb-8 bg-red-50 border-red-200">
            <AlertDescription className="text-red-700">
              {generateMutation.error?.message || "Failed to generate speech. Please try again."}
            </AlertDescription>
          </Alert>
        )}

        {/* Audio Player */}
        {audioUrl && (
          <AudioPlayer
            audioUrl={audioUrl}
            onDownload={() => {
              toast({
                title: "Download Started",
                description: "Your audio file is being downloaded",
              });
            }}
          />
        )}
      </CardContent>
    </Card>
  );
}
