import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft } from "lucide-react";
import { Link } from "wouter";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

export default function Report() {
  const [formData, setFormData] = useState({
    title: "",
    category: "",
    priority: "",
    description: "",
    stepsToReproduce: "",
    expectedBehavior: "",
    actualBehavior: "",
    browser: "",
    operatingSystem: "",
    deviceType: "",
    includeSystemInfo: true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1500));

    toast({
      title: "Issue Reported",
      description: "Thank you for reporting this issue. Our team will investigate and get back to you soon.",
    });

    setFormData({
      title: "",
      category: "",
      priority: "",
      description: "",
      stepsToReproduce: "",
      expectedBehavior: "",
      actualBehavior: "",
      browser: "",
      operatingSystem: "",
      deviceType: "",
      includeSystemInfo: true
    });
    setIsSubmitting(false);
  };

  const handleChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
        
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Report an Issue</h1>
          <p className="text-lg text-gray-600">Help us improve Speech AI by reporting bugs and issues</p>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Issue Report Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Issue Details</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <Label htmlFor="title">Issue Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleChange("title", e.target.value)}
                      required
                      placeholder="Brief description of the issue"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="category">Category *</Label>
                      <Select value={formData.category} onValueChange={(value) => handleChange("category", value)} required>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="audio-generation">Audio Generation</SelectItem>
                          <SelectItem value="audio-playback">Audio Playback</SelectItem>
                          <SelectItem value="download-issues">Download Issues</SelectItem>
                          <SelectItem value="voice-selection">Voice Selection</SelectItem>
                          <SelectItem value="language-support">Language Support</SelectItem>
                          <SelectItem value="user-interface">User Interface</SelectItem>
                          <SelectItem value="performance">Performance</SelectItem>
                          <SelectItem value="mobile-issues">Mobile Issues</SelectItem>
                          <SelectItem value="browser-compatibility">Browser Compatibility</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="priority">Priority</Label>
                      <Select value={formData.priority} onValueChange={(value) => handleChange("priority", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low - Minor inconvenience</SelectItem>
                          <SelectItem value="medium">Medium - Affects functionality</SelectItem>
                          <SelectItem value="high">High - Blocks main features</SelectItem>
                          <SelectItem value="critical">Critical - Service unavailable</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleChange("description", e.target.value)}
                      required
                      placeholder="Describe the issue in detail..."
                      rows={4}
                    />
                  </div>

                  <div>
                    <Label htmlFor="stepsToReproduce">Steps to Reproduce</Label>
                    <Textarea
                      id="stepsToReproduce"
                      value={formData.stepsToReproduce}
                      onChange={(e) => handleChange("stepsToReproduce", e.target.value)}
                      placeholder="1. Go to...&#10;2. Click on...&#10;3. Enter text...&#10;4. See error"
                      rows={5}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="expectedBehavior">Expected Behavior</Label>
                      <Textarea
                        id="expectedBehavior"
                        value={formData.expectedBehavior}
                        onChange={(e) => handleChange("expectedBehavior", e.target.value)}
                        placeholder="What should happen?"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="actualBehavior">Actual Behavior</Label>
                      <Textarea
                        id="actualBehavior"
                        value={formData.actualBehavior}
                        onChange={(e) => handleChange("actualBehavior", e.target.value)}
                        placeholder="What actually happens?"
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">System Information</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <Label htmlFor="browser">Browser</Label>
                        <Input
                          id="browser"
                          value={formData.browser}
                          onChange={(e) => handleChange("browser", e.target.value)}
                          placeholder="Chrome 120, Firefox 115, etc."
                        />
                      </div>

                      <div>
                        <Label htmlFor="operatingSystem">Operating System</Label>
                        <Input
                          id="operatingSystem"
                          value={formData.operatingSystem}
                          onChange={(e) => handleChange("operatingSystem", e.target.value)}
                          placeholder="Windows 11, macOS 14, etc."
                        />
                      </div>

                      <div>
                        <Label htmlFor="deviceType">Device Type</Label>
                        <Select value={formData.deviceType} onValueChange={(value) => handleChange("deviceType", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select device" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="desktop">Desktop</SelectItem>
                            <SelectItem value="laptop">Laptop</SelectItem>
                            <SelectItem value="tablet">Tablet</SelectItem>
                            <SelectItem value="mobile">Mobile Phone</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeSystemInfo"
                        checked={formData.includeSystemInfo}
                        onCheckedChange={(checked) => handleChange("includeSystemInfo", checked as boolean)}
                      />
                      <Label htmlFor="includeSystemInfo" className="text-sm">
                        Include additional system information to help with debugging
                      </Label>
                    </div>
                  </div>

                  <Button type="submit" disabled={isSubmitting} className="w-full">
                    {isSubmitting ? "Submitting Report..." : "Submit Issue Report"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Help & Guidelines */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Reporting Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Before Reporting</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Check our Help Center for solutions</li>
                    <li>• Try refreshing the page</li>
                    <li>• Test in a different browser</li>
                    <li>• Clear browser cache and cookies</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Good Bug Reports Include</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Clear, descriptive title</li>
                    <li>• Step-by-step reproduction</li>
                    <li>• Expected vs actual behavior</li>
                    <li>• Browser and system details</li>
                    <li>• Screenshots if applicable</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Response Time</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Critical: Within 4 hours</li>
                    <li>• High: Within 24 hours</li>
                    <li>• Medium: Within 3 days</li>
                    <li>• Low: Within 1 week</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Common Issues</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Audio Not Playing</h4>
                  <p className="text-sm text-gray-600">
                    Check browser permissions, ensure speakers are on, try different browsers.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Download Not Working</h4>
                  <p className="text-sm text-gray-600">
                    Generate speech first, check download settings, try different browsers.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Voice Not Available</h4>
                  <p className="text-sm text-gray-600">
                    Some voices may not be supported on all browsers or operating systems.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Mobile Issues</h4>
                  <p className="text-sm text-gray-600">
                    Mobile browsers may have different limitations for audio playback and downloads.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alternative Support</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <a href="/help" className="block text-blue-600 hover:text-blue-800 text-sm">
                  Help Center →
                </a>
                <a href="/contact" className="block text-blue-600 hover:text-blue-800 text-sm">
                  Contact Support →
                </a>
                <a href="/api-docs" className="block text-blue-600 hover:text-blue-800 text-sm">
                  API Documentation →
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}