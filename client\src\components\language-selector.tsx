import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Globe, User } from "lucide-react";
import { Language, Voice } from "@/lib/languages";

interface LanguageSelectorProps {
  languages: Language[];
  voices: Voice[];
  selectedLanguage: string;
  selectedVoice: string;
  onLanguageChange: (language: string) => void;
  onVoiceChange: (voice: string) => void;
  isLoading?: boolean;
}

export function LanguageSelector({
  languages,
  voices,
  selectedLanguage,
  selectedVoice,
  onLanguageChange,
  onVoiceChange,
  isLoading = false
}: LanguageSelectorProps) {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      <div>
        <Label className="flex items-center text-sm font-medium text-slate-700 mb-2">
          <Globe className="w-4 h-4 mr-2" />
          Select Language
        </Label>
        <Select value={selectedLanguage} onValueChange={onLanguageChange} disabled={isLoading}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose a language" />
          </SelectTrigger>
          <SelectContent>
            {languages.map((language) => (
              <SelectItem key={language.code} value={language.code}>
                <span className="flex items-center">
                  <span className="mr-2">{language.flag}</span>
                  {language.name}
                </span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label className="flex items-center text-sm font-medium text-slate-700 mb-2">
          <User className="w-4 h-4 mr-2" />
          Select Voice
        </Label>
        <Select value={selectedVoice} onValueChange={onVoiceChange} disabled={isLoading}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose a voice" />
          </SelectTrigger>
          <SelectContent>
            {voices.map((voice) => (
              <SelectItem key={voice.id} value={voice.id}>
                {voice.name} ({voice.gender === 'male' ? 'Male' : 'Female'})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
