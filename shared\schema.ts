import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const ttsRequests = pgTable("tts_requests", {
  id: serial("id").primaryKey(),
  text: text("text").notNull(),
  language: text("language").notNull(),
  voice: text("voice").notNull(),
  speed: text("speed").notNull().default("1.0"),
  pitch: text("pitch").notNull().default("0"),
  audioUrl: text("audio_url"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertTtsRequestSchema = createInsertSchema(ttsRequests).pick({
  text: true,
  language: true,
  voice: true,
  speed: true,
  pitch: true,
});

export type InsertTtsRequest = z.infer<typeof insertTtsRequestSchema>;
export type TtsRequest = typeof ttsRequests.$inferSelect;

// Language and voice types
export interface Language {
  code: string;
  name: string;
  flag: string;
}

export interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  language: string;
}

export interface TtsApiRequest {
  text: string;
  language: string;
  voice: string;
  speed: string;
  pitch: string;
}

export interface TtsApiResponse {
  success: boolean;
  audioUrl?: string;
  error?: string;
}
