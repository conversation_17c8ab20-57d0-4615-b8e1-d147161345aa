import { ttsRequests, type TtsRequest, type InsertTtsRequest } from "@shared/schema";

export interface IStorage {
  createTtsRequest(request: InsertTtsRequest): Promise<TtsRequest>;
  getTtsRequest(id: number): Promise<TtsRequest | undefined>;
  updateTtsRequestAudio(id: number, audioUrl: string): Promise<TtsRequest | undefined>;
}

export class MemStorage implements IStorage {
  private requests: Map<number, TtsRequest>;
  private currentId: number;

  constructor() {
    this.requests = new Map();
    this.currentId = 1;
  }

  async createTtsRequest(insertRequest: InsertTtsRequest): Promise<TtsRequest> {
    const id = this.currentId++;
    const request: TtsRequest = {
      id,
      text: insertRequest.text,
      language: insertRequest.language,
      voice: insertRequest.voice,
      speed: insertRequest.speed || "1.0",
      pitch: insertRequest.pitch || "0",
      audioUrl: null,
      createdAt: new Date(),
    };
    this.requests.set(id, request);
    return request;
  }

  async getTtsRequest(id: number): Promise<TtsRequest | undefined> {
    return this.requests.get(id);
  }

  async updateTtsRequestAudio(id: number, audioUrl: string): Promise<TtsRequest | undefined> {
    const request = this.requests.get(id);
    if (request) {
      request.audioUrl = audioUrl;
      this.requests.set(id, request);
      return request;
    }
    return undefined;
  }
}

export const storage = new MemStorage();
