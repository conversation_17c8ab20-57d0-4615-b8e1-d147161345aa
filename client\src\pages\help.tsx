import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ArrowLeft } from "lucide-react";
import { Link } from "wouter";

export default function Help() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
        
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Help Center</h1>
          <p className="text-lg text-gray-600">Find answers to common questions about Speech AI</p>
        </div>

        <div className="grid gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible>
                <AccordionItem value="how-to-use">
                  <AccordionTrigger>How do I use Speech AI?</AccordionTrigger>
                  <AccordionContent>
                    <ol className="list-decimal list-inside space-y-2">
                      <li>Enter your text in the text area</li>
                      <li>Select your preferred language from the dropdown</li>
                      <li>Choose a voice (male or female)</li>
                      <li>Adjust speech speed and pitch if needed</li>
                      <li>Click "Generate Speech" to create audio</li>
                      <li>Use the play button to listen or download the audio file</li>
                    </ol>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="supported-languages">
                  <AccordionTrigger>What languages are supported?</AccordionTrigger>
                  <AccordionContent>
                    Speech AI supports over 100 languages and regional variants including:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>English (US, UK, Australia, India, and more)</li>
                      <li>Spanish (Spain, Mexico, Argentina, and more)</li>
                      <li>French (France, Canada)</li>
                      <li>German, Italian, Portuguese</li>
                      <li>Chinese (Mandarin, Cantonese)</li>
                      <li>Japanese, Korean, Arabic</li>
                      <li>And many more regional variants</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="voice-options">
                  <AccordionTrigger>What voice options are available?</AccordionTrigger>
                  <AccordionContent>
                    Each language offers multiple voice options:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Male and female voices</li>
                      <li>Different age ranges and speaking styles</li>
                      <li>Adjustable speech speed (0.5x to 2x)</li>
                      <li>Pitch control for voice customization</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Audio & Downloads</CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible>
                <AccordionItem value="download-formats">
                  <AccordionTrigger>What audio formats are available?</AccordionTrigger>
                  <AccordionContent>
                    TTSMaker provides audio files in high-quality formats:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>WAV format for best quality</li>
                      <li>MP3 format when using external API services</li>
                      <li>44.1kHz sample rate for professional quality</li>
                      <li>No watermarks or time limits</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="download-issues">
                  <AccordionTrigger>Why isn't my download working?</AccordionTrigger>
                  <AccordionContent>
                    If downloads aren't working, try these solutions:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Generate speech first by clicking "Generate Speech"</li>
                      <li>Wait for the audio to be fully processed</li>
                      <li>Check your browser's download settings</li>
                      <li>Try a different browser if issues persist</li>
                      <li>Ensure you have sufficient storage space</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="text-limits">
                  <AccordionTrigger>Are there text length limits?</AccordionTrigger>
                  <AccordionContent>
                    TTSMaker is designed for flexible use:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>No strict character limits for most use cases</li>
                      <li>Longer texts may take more time to process</li>
                      <li>For very long documents, consider splitting into sections</li>
                      <li>Performance is optimized for texts up to 1000 words</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Technical Support</CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible>
                <AccordionItem value="browser-support">
                  <AccordionTrigger>Which browsers are supported?</AccordionTrigger>
                  <AccordionContent>
                    TTSMaker works best on modern browsers:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Chrome 80+ (recommended)</li>
                      <li>Firefox 75+</li>
                      <li>Safari 13+</li>
                      <li>Edge 80+</li>
                      <li>Mobile browsers on iOS and Android</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="troubleshooting">
                  <AccordionTrigger>Common troubleshooting steps</AccordionTrigger>
                  <AccordionContent>
                    If you're experiencing issues:
                    <ol className="list-decimal list-inside mt-2 space-y-1">
                      <li>Refresh the page and try again</li>
                      <li>Clear your browser cache and cookies</li>
                      <li>Disable browser extensions temporarily</li>
                      <li>Check your internet connection</li>
                      <li>Try using an incognito/private browsing window</li>
                      <li>Update your browser to the latest version</li>
                    </ol>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="mobile-usage">
                  <AccordionTrigger>Can I use TTSMaker on mobile devices?</AccordionTrigger>
                  <AccordionContent>
                    Yes! TTSMaker is fully responsive and works on:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>iPhone and iPad (Safari, Chrome)</li>
                      <li>Android phones and tablets</li>
                      <li>Touch-optimized interface</li>
                      <li>All features available on mobile</li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Still Need Help?</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="/contact"
                className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Contact Support
              </a>
              <a
                href="/report"
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Report an Issue
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}