import { TtsConverter } from "@/components/tts-converter";
import { Footer } from "@/components/footer";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Mic2, Globe, Brain, GraduationCap, Accessibility, Podcast, Briefcase } from "lucide-react";
import { <PERSON> } from "wouter";

export default function Home() {
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <Mic2 className="text-white w-5 h-5" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900">Speech AI</h1>
                <p className="text-xs text-slate-500">Free Text to Speech</p>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-slate-600 hover:text-blue-600 transition-colors">Home</Link>
              <Link href="/api-docs" className="text-slate-600 hover:text-blue-600 transition-colors">API</Link>
              <Link href="/contact" className="text-slate-600 hover:text-blue-600 transition-colors">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            Free Text to Speech Converter
          </h2>
          <p className="text-xl text-slate-600 mb-8">
            Transform your text into natural-sounding speech with our AI-powered voice generator. 
            Support for 100+ languages and voices.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-slate-600">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>100% Free</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>No Registration Required</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>100+ Languages</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>Natural AI Voices</span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Converter */}
      <main className="max-w-6xl mx-auto px-4 py-12">
        <TtsConverter />
      </main>

      {/* Features Section */}
      <section className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Why Choose Our Text to Speech Converter?</h2>
          <p className="text-xl text-slate-600">Advanced AI technology for natural-sounding speech synthesis</p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <img 
              src="https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" 
              alt="Professional microphone recording setup" 
              className="rounded-xl shadow-lg w-full h-48 object-cover mb-6" 
            />
            <h3 className="text-xl font-semibold text-slate-900 mb-3">High-Quality Audio</h3>
            <p className="text-slate-600">Generate crystal-clear speech with natural intonation and pronunciation using advanced AI algorithms.</p>
          </div>
          
          <div className="text-center">
            <img 
              src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" 
              alt="Diverse group representing multilingual support" 
              className="rounded-xl shadow-lg w-full h-48 object-cover mb-6" 
            />
            <h3 className="text-xl font-semibold text-slate-900 mb-3">100+ Languages</h3>
            <p className="text-slate-600">Support for over 100 languages and dialects with native speaker quality voices and accents.</p>
          </div>
          
          <div className="text-center">
            <img 
              src="https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" 
              alt="AI speech technology visualization" 
              className="rounded-xl shadow-lg w-full h-48 object-cover mb-6" 
            />
            <h3 className="text-xl font-semibold text-slate-900 mb-3">AI-Powered</h3>
            <p className="text-slate-600">Cutting-edge artificial intelligence delivers human-like speech patterns and emotional expression.</p>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="bg-white py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 mb-4">Perfect for Every Use Case</h2>
            <p className="text-xl text-slate-600">From education to entertainment, our TTS tool serves diverse needs</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-slate-50 hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <GraduationCap className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">Education</h3>
                <p className="text-sm text-slate-600">Create audio lessons and study materials</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-50 hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Accessibility className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">Accessibility</h3>
                <p className="text-sm text-slate-600">Make content accessible for visually impaired users</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-50 hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Podcast className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">Content Creation</h3>
                <p className="text-sm text-slate-600">Generate voiceovers for videos and podcasts</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-50 hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Briefcase className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">Business</h3>
                <p className="text-sm text-slate-600">Create professional audio presentations</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
}
